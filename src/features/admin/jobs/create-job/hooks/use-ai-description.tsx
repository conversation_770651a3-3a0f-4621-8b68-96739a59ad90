import { useJobStore } from './use-job-store';
import {
  useGenerateJobDescription as useGenerateJobDescriptionQuery,
  useUpdateJobMutation,
} from '@/hooks/api/use-job';
import { getGenerateJobDescription } from '@/services/job';
import { useState } from 'react';
import { toast } from 'sonner';

export const useAiDescription = (jobId?: string) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const { updateFormData } = useJobStore();
  const updateJobMutation = useUpdateJobMutation();

  const { data, isLoading, error } = useGenerateJobDescriptionQuery(jobId || '', {
    enabled: isEnabled && !!jobId,
  });

  const parseHtmlContent = (rawContent: string): string => {
    if (!rawContent) return '';

    // Remove markdown code block syntax and clean up the content
    const cleanedContent = rawContent
      .replace(/```html\n?/g, '') // Remove opening ```html
      .replace(/```\n?/g, '') // Remove closing ```
      .replace(/\\n/g, '\n') // Replace escaped newlines with actual newlines
      .replace(/\n/g, '') // Remove actual newlines to let HTML handle formatting
      .trim();

    return cleanedContent;
  };

  // const generateJobDescription = async (newJobId?: string): Promise<string | null> => {
  //   setIsEnabled(true);
  //   if (!jobId && !newJobId) {
  //     toast.error('Job needs to be created to generate description');
  //     return null;
  //   }

  //   try {
  //     const result = await refetch();

  //     if (result.data) {
  //       const parsedContent = parseHtmlContent(result.data);
  //       // toast.success('Job description generated successfully');
  //       return parsedContent;
  //     } else {
  //       toast.error('Failed to generate job description');
  //       return null;
  //     }
  //   } catch (err) {
  //     const errorMessage =
  //       err instanceof Error ? err.message : 'An error occurred while generating job description';
  //     toast.error(errorMessage);
  //     return null;
  //   } finally {
  //     setIsEnabled(false);
  //   }
  // };

  const generateJobDescription = async (newJobId?: string): Promise<string | null> => {
    setIsEnabled(true);
    const idToUse = newJobId ?? jobId;

    if (!idToUse) {
      toast.error('Job needs to be created to generate description');
      return null;
    }

    try {
      let result;

      if (idToUse) {
        const response = await getGenerateJobDescription(idToUse);
        result = response.data;
      }

      if (result) {
        const parsedContent = parseHtmlContent(result);
        return parsedContent;
      } else {
        toast.error('Failed to generate job description');
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while generating job description';
      toast.error(errorMessage);
      return null;
    } finally {
      setIsEnabled(false);
    }
  };

  const saveDescription = async (
    description: string,
    additionalPayload?: Partial<JobInformation>
  ) => {
    if (!jobId) {
      toast.error('Job ID is required for saving description');
      return false;
    }

    try {
      await updateJobMutation.mutateAsync({
        id: jobId,
        payload: {
          description,
          ...additionalPayload,
        } as JobInformation,
      });
      updateFormData({
        description,
      });
      return true;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while saving job description';
      toast.error(errorMessage);
      return false;
    }
  };

  return {
    generateJobDescription,
    saveDescription,
    isSaving: updateJobMutation.isPending,
    isGenerating: isLoading || isEnabled,
    error,
    parsedDescription: data ? parseHtmlContent(data) : null,
  };
};
