'use client';

import { useJobStore } from '../hooks/use-job-store';
import UploadCloud from '@/assets/icons/upload-cloud';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Label,
} from '@/components/ui';
import JobInformation from '@/features/candidate/application-form/components/job-information';
import { useUpdateJobMutation } from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { JOB_STATUS } from '@/utils/constants';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { toast } from 'sonner';

export function PreviewStep() {
  const navigate = useNavigate();
  const { formData, setCurrentStep, resetForm } = useJobStore();
  const { draftJobId, clearDraftJobId } = useJobCreationStore();
  const [isSavingDraft, setIsSavingDraft] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  const { mutate: updateJobStatus } = useUpdateJobMutation();

  const handleJobStatusChange = (status: string) => {
    if (status === JOB_STATUS.DRAFT) {
      setIsSavingDraft(true);
    } else if (status === JOB_STATUS.ACTIVE) {
      setIsPublishing(true);
    }

    if (!draftJobId) {
      toast.error('No job found. Please save job information first');
      setIsSavingDraft(false);
      setIsPublishing(false);
      return;
    }

    updateJobStatus(
      { id: draftJobId, payload: { status } },

      {
        onSuccess: () => {
          if (status === JOB_STATUS.ACTIVE) {
            toast.success('Job published successfully');
          } else {
            toast.success('Job saved as draft');
          }
          clearDraftJobId();
          resetForm();
        },
        onSettled: () => {
          setIsSavingDraft(false);
          setIsPublishing(false);
          setCurrentStep(1);
          navigate({ to: '/admin/dashboard' });
        },
      }
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-4xl px-2'
    >
      <Card className='border-none bg-transparent'>
        <CardHeader className='mx-auto w-2xl text-center'>
          <CardTitle className='text-xl'>Review and Test Before Publishing</CardTitle>
          <CardDescription>
            Review your job post as candidates will see it, and test your selected AI interview
            agent using a sample or uploaded CV. Then you're ready to publish.
          </CardDescription>
        </CardHeader>
        <CardContent className='border-strock mt-4 space-y-6 overflow-hidden rounded-2xl border p-0'>
          <JobInformation
            job={{
              description: formData.description,
              title: formData.title,
              max_exp: parseInt(formData.maxExperience),
              location: formData.location,
            }}
          />

          <form className='px-8 py-4'>
            <h3 className='text-gray-dark mb-8 text-2xl'>Application Form</h3>

            <div className='space-y-6'>
              <div>
                <Label className='mb-3 font-bold'>Full Name</Label>
                <Input placeholder='Enter your full name' disabled />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Label className='mb-3 font-bold'>Email</Label>
                  <Input placeholder='Enter your email' disabled />
                </div>

                <div>
                  <Label className='mb-3 font-bold'>Phone Number</Label>
                  <Input placeholder='Enter your phone number' disabled />
                </div>
              </div>

              <div>
                <Label className='mb-3 font-bold'>Address</Label>
                <Input placeholder='Enter your full name' disabled />
              </div>

              <div>
                <Label className='mb-3 font-bold'>Year of experience</Label>
                <Input placeholder='Enter your full name' disabled />
              </div>

              <div>
                <Label className='mb-3 font-bold'>Resume/CV</Label>
                <div className='border-primary rounded-lg border-2 border-dashed'>
                  <div className='py-10 text-center'>
                    <UploadCloud className='mx-auto mb-2' />
                    <p className='font-medium text-black'>
                      Drag your files or <span className='text-primary'>browse </span>
                    </p>
                    <p className='text-gray-light mt-1 text-sm'>Max 2 MB files are allowed</p>
                  </div>
                </div>
                <p className='text-gray-light mt-3 text-sm'>Only support PDF and DOC files</p>
              </div>
            </div>

            <Button type='submit' className='pointer-events-none my-4 w-40'>
              Submit application
            </Button>
          </form>
        </CardContent>

        <div className='flex items-center justify-center gap-4'>
          <Button
            variant='secondary'
            className='w-63'
            disabled={isSavingDraft || isPublishing}
            loading={isSavingDraft}
            onClick={() => handleJobStatusChange(JOB_STATUS.DRAFT)}
          >
            Save as Draft Job Post
          </Button>
          <Button
            className='w-63'
            disabled={isSavingDraft || isPublishing}
            loading={isPublishing}
            onClick={() => handleJobStatusChange(JOB_STATUS.ACTIVE)}
          >
            Publish Job Post
          </Button>
        </div>
      </Card>
    </motion.div>
  );
}
