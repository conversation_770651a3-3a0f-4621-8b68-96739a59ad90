import { durationOptions } from '../../utils/data';
import { calculateTotalDuration, formatPersonaOptions, generateUniqueId } from '../../utils/helper';
import { DraggablePersonaList } from './draggable-persona-list';
import { AsyncSelector } from '@/components/select/async-selector';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/use-job-store';
import { useAttachPersonaToJobMutation, usePersonasQuery } from '@/hooks/api/use-persona';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { motion } from 'framer-motion';
import { forwardRef, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

export const InterviewAgentStep = forwardRef<{ triggerValidation: () => Promise<boolean> }>(() => {
  const { formData, updateFormData } = useJobStore();
  const [selectedDuration, setSelectedDuration] = useState('');
  const [selectedPersona, setSelectedPersona] = useState('');

  const { draftJobId } = useJobCreationStore();
  const { nextStep } = useJobStore();

  const { data: allPersonas } = usePersonasQuery();
  const { mutate: attachToJob, isPending } = useAttachPersonaToJobMutation();

  const [personas, setPersonas] = useState<InterviewPersona[]>([]);

  useEffect(() => {
    if (formData.totalPersonas > 0) {
      setPersonas(formData.interviewPersonas);
    }
  }, [formData.interviewPersonas, formData.totalPersonas]);

  const handleAddPersona = () => {
    if (selectedPersona && selectedDuration) {
      const newPersona: InterviewPersona = {
        id: allPersonas?.find((p) => p.title === selectedPersona)?.id || generateUniqueId(),
        persona: selectedPersona,
        duration: selectedDuration,
      };
      const updatedPersonas = [...formData.interviewPersonas, newPersona];
      updateFormData({
        interviewPersonas: updatedPersonas,
        totalPersonas: updatedPersonas.length,
        totalDuration: calculateTotalDuration(updatedPersonas),
      });

      setPersonas(updatedPersonas);
      setSelectedPersona('');
      setSelectedDuration('');
    }
  };

  const selectedPersonaIds = formData.interviewPersonas.map((p) => p.id);
  const availablePersonas = useMemo(
    () => allPersonas?.filter((p) => !selectedPersonaIds.includes(p.id)) || [],
    [allPersonas, selectedPersonaIds]
  );

  const handleSave = () => {
    if (!draftJobId) {
      toast.error('No job found. Please save job information first');
      return;
    }

    const payload = personas.map((persona) => {
      return {
        persona_id: persona.id,
        time_duration: parseInt(persona.duration),
        execution_order: personas.findIndex((p) => p.id === persona.id) + 1,
      };
    });

    attachToJob(
      { jobId: draftJobId, list: { personas: payload } },
      {
        onSuccess: () => {
          nextStep();
        },
      }
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-2xl'
    >
      <Card className='border-none bg-transparent'>
        <CardHeader className='text-center'>
          <CardTitle className='text-xl'>Select Your AI Interview Personas</CardTitle>
          <CardDescription>
            Choose AI personas best suited for this role. If you don&apos;t find a perfect match,
            you can create a new custom persona tailored to your needs.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='grid grid-cols-2 rounded-lg bg-white p-4'>
            <div className='text-left'>
              <div className='text-gray-light mb-1 text-sm'>Total Interview Persona</div>
              <div className='font-medium text-black'>{formData.totalPersonas}</div>
            </div>
            <div className='text-left'>
              <div className='text-gray-light mb-1 text-sm'>Total Duration</div>
              <div className='font-medium text-black'>{formData.totalDuration}</div>
            </div>
          </div>

          {personas.length > 0 && (
            <DraggablePersonaList personas={personas} setPersonas={setPersonas} />
          )}

          <div className='border-strock space-y-6 rounded-2xl border bg-white p-10'>
            <div className='space-y-3'>
              <Label htmlFor='interviewAgent'>Interview Persona</Label>
              <AsyncSelector
                options={formatPersonaOptions(availablePersonas)}
                value={selectedPersona}
                onChange={(value) => setSelectedPersona(value)}
                placeholder='Select Persona'
              />
            </div>

            <div className='space-y-3'>
              <Label htmlFor='interviewDuration'>Interview Duration</Label>
              <Select
                value={selectedDuration}
                onValueChange={(value) => setSelectedDuration(value)}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Select Duration' />
                </SelectTrigger>
                <SelectContent>
                  {durationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <Button variant='secondary' disabled>
                Create a new interview persona
              </Button>
              <Button disabled={!(selectedPersona && selectedDuration)} onClick={handleAddPersona}>
                Add
              </Button>
            </div>
          </div>
          {personas.length > 0 && (
            <div className='grid place-content-center'>
              <Button
                className='mt-4 flex w-63 items-center text-center'
                loading={isPending}
                onClick={handleSave}
              >
                Save & Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
});

InterviewAgentStep.displayName = 'InterviewAgentStep';
