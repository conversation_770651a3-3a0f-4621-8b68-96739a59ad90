import { JobActionDropdown } from './components/job-action-dropdown';
import { JobTabContents } from './job-tab-contents';
import { Loader } from '@/components/shared/loader';
import { Badge } from '@/components/ui';
import { JobNotFound } from '@/features/candidate/application-form/components/job-not-found';
import { useJobDetailsForAdminQuery } from '@/hooks/api/use-job';
import { ErrorCode } from '@/utils/constants';
import { formatInterviewDuration, getStatusBadgeVariant } from '@/utils/helper';
import { useParams } from '@tanstack/react-router';
import { format, formatDistance } from 'date-fns';
import { MapPin } from 'lucide-react';

const JobDetails = () => {
  const { jobId } = useParams({ from: '/_authenticated/admin/jobs/$jobId/' });

  const { data: jobData, isLoading, isError } = useJobDetailsForAdminQuery(jobId || '');

  if (isLoading) {
    return <Loader type='page' />;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if (isError || !jobData || (jobData as any)?.code === ErrorCode.NOT_FOUND) {
    return <JobNotFound />;
  }

  return (
    <div className='bg-custom-white min-h-screen p-6'>
      <section className='border-strock rounded-2xl border bg-white p-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <div className='mb-2 flex items-center gap-2'>
              <h1 className='text-2xl text-black'>{jobData?.title}</h1>
              <Badge variant={getStatusBadgeVariant(jobData?.status)} className='h-7.5 capitalize'>
                {jobData?.status}
              </Badge>
            </div>

            <div className='text-gray-light flex items-center gap-1'>
              <MapPin className='size-4' />
              <p>{jobData?.location}</p>
            </div>
          </div>

          {jobData && <JobActionDropdown jobData={jobData} />}
        </div>

        {/* Stats */}
        <div className='border-strock my-6 grid grid-cols-5 gap-2 rounded-md border p-4'>
          <div>
            <p className='text-gray-light mb-1 text-sm'>Expertise</p>
            <p className='text-base font-medium text-black'>
              {jobData?.min_exp} - {jobData?.max_exp} Years
            </p>
          </div>
          <div>
            <p className='text-gray-light mb-1 text-sm'>Total Applicants</p>
            <p className='text-base font-medium text-black'>{jobData?.candidate_count.total}</p>
          </div>
          <div>
            <p className='text-gray-light mb-1 text-sm'>Time left</p>
            <p className='text-base font-medium text-black'>
              {jobData?.application_end_date
                ? formatDistance(jobData.application_end_date, new Date(), { addSuffix: false })
                : '-'}
            </p>
          </div>
          <div>
            <p className='text-gray-light mb-1 text-sm'>Interview Duration</p>
            <p className='text-base font-medium text-black'>
              {formatInterviewDuration(jobData?.interview_duration)}
            </p>
          </div>
          <div>
            <p className='text-gray-light mb-1 text-sm'>Created At</p>
            <p className='text-base font-medium text-black'>
              {jobData?.application_start_date
                ? format(jobData.application_start_date, 'dd MMM yyyy')
                : '-'}
            </p>
          </div>
        </div>

        <JobTabContents
          jobDescription={jobData?.description || ''}
          filterCriteria={jobData?.initial_filter_criteria || ''}
        />
      </section>
    </div>
  );
};
export default JobDetails;
