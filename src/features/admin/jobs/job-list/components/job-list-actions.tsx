import { INACTIVE, ACTIVE } from '../utils/constant';
import { ConfirmationModal } from '@/components/shared/confirmation-modal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useUpdateJobMutation, useDeleteJobMutation } from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { useNavigate } from '@tanstack/react-router';
import { Ellipsis, Edit, Trash2, X } from 'lucide-react';
import React, { useState } from 'react';

interface JobListActionsProps {
  job: JobList;
}

export const JobListActions: React.FC<JobListActionsProps> = ({ job }) => {
  const navigate = useNavigate();
  const { setDraftJobId } = useJobCreationStore();
  const { mutate: updateJobStatus } = useUpdateJobMutation();
  const { mutateAsync: deleteJob } = useDeleteJobMutation();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const handleEdit = () => {
    setDraftJobId(job.id);
    navigate({ to: '/admin/jobs/edit' });
  };

  const handleMarkInactive = () => {
    updateJobStatus({
      id: job.id,
      payload: { status: INACTIVE },
    });
  };

  const handleDeleteJob = async () => {
    await deleteJob(job.id);
    setIsConfirmModalOpen(false);
  };

  const handleRemoveClick = () => {
    setIsConfirmModalOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className='rounded-sm p-1 hover:bg-neutral-200'>
            <Ellipsis className='size-4 cursor-pointer' />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='font-manrope min-w-[120px]'>
          <DropdownMenuItem onClick={handleEdit} disabled={job.status === ACTIVE}>
            <Edit className='mr-2 size-4' /> Edit job post
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleMarkInactive} disabled={job.status !== ACTIVE}>
            <X className='mr-2 size-4' /> Mark as Inactive
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={handleRemoveClick}
            className='text-destructive hover:bg-destructive/10 focus:bg-destructive/10'
          >
            <Trash2 className='text-destructive mr-2 size-4' /> Remove
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ConfirmationModal
        title={`Remove Job`}
        description={`Are you sure you want to remove ${job.title} job post? This action cannot be undone.`}
        onConfirm={handleDeleteJob}
        open={isConfirmModalOpen}
        onOpenChange={setIsConfirmModalOpen}
      />
    </>
  );
};
