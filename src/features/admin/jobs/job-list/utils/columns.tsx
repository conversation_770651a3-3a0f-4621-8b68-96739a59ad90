import { JobListActions } from '../components/job-list-actions';
import { Badge } from '@/components/ui';
import { getStatusBadgeVariant, getTimeLeft, snakeToTitleCase } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';

export const jobColumns: ColumnDef<JobList>[] = [
  {
    accessorKey: 'title',
    header: 'Job Title',
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => row.original.location || '-',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={getStatusBadgeVariant(row.original.status)}>
        {snakeToTitleCase(row.original.status)}
      </Badge>
    ),
  },
  {
    accessorKey: 'experience',
    header: 'Experience (years)',
    meta: {
      align: 'center',
    },
    cell: ({ row }) => {
      const { min_exp, max_exp } = row.original;
      if (min_exp || max_exp) return `${min_exp ?? '-'} - ${max_exp ?? '-'}`;
      return '-';
    },
  },
  {
    accessorKey: 'timeleft',
    header: 'Time Left',
    cell: ({ row }) => {
      const timeLeft = getTimeLeft(row.original.application_end_date);

      return (
        <div className={timeLeft === 'Closed' ? 'font-medium text-red-600' : ''}>{timeLeft}</div>
      );
    },
  },
  {
    accessorKey: 'totalCandidates',
    header: 'Total Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.total ?? 0}
      </div>
    ),
  },
  {
    accessorKey: 'eligibleCandidates',
    header: 'Eligible Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.eligible ?? '-'}
      </div>
    ),
  },
  {
    accessorKey: 'attendedCandidates',
    header: 'Attended Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.attended ?? 0}
      </div>
    ),
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div
        className='flex items-center justify-center text-center'
        onClick={(e) => e.stopPropagation()}
      >
        <JobListActions job={row.original} />
      </div>
    ),
  },
];
