import { ApplicantCV } from './applicant-cv';
import { EvaluationReport } from './evaluation-report';
import { InterviewDetails } from './interview-details';
import { DataTabs } from '@/components/data-tabs';

interface Props {
  applicantData: ApplicantDetails;
  isLoading: boolean;
}

export const ApplicantTabContents = ({ applicantData, isLoading }: Props) => {
  const interviews = applicantData?.interviews || [];
  const latestInterview = interviews.length > 0 ? interviews[interviews.length - 1] : null;

  const tabConfig: TabItem[] = [
    {
      label: 'Evaluation Report',
      value: 'evaluation-report',
      content: () => (
        <EvaluationReport
          evaluationData={latestInterview?.evaluation?.evaluation_data ?? null}
          isEvaluationLoading={isLoading}
        />
      ),
    },
    {
      label: "Applicant's CV",
      value: 'applicant-cv',
      content: () => <ApplicantCV cvLink={applicantData?.cv_link} />,
    },
    {
      label: 'Interview Details',
      value: 'interview-details',
      content: () => <InterviewDetails interviews={latestInterview} />,
    },
  ];
  return (
    <div>
      <DataTabs items={tabConfig} contentHeight='calc(100vh - 355px)' />
    </div>
  );
};
