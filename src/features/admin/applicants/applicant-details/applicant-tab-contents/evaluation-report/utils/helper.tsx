import { EvaluationReportData } from '../analysis-report';
import { Award, Clock, Hourglass, XCircle } from 'lucide-react';

export const getVerdictColor = (status: string) => {
  switch (status) {
    case 'Strong':
      return 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-200 dark:from-green-950/20 dark:to-emerald-950/20 dark:text-green-400 dark:border-green-800';
    case 'Concerning':
      return 'bg-gradient-to-r from-red-50 to-rose-50 text-red-800 border-red-200 dark:from-red-950/20 dark:to-rose-950/20 dark:text-red-400 dark:border-red-800';
    case 'Adequate':
      return 'bg-gradient-to-r from-orange-50 to-amber-50 text-orange-800 border-orange-200 dark:from-orange-950/20 dark:to-amber-950/20 dark:text-orange-400 dark:border-orange-800';
    default:
      return 'bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-800 border-blue-200 dark:from-blue-950/20 dark:to-cyan-950/20 dark:text-blue-400 dark:border-blue-800';
  }
};

export const getVerdictIcon = (status: string) => {
  switch (status) {
    case 'Strong':
      return <Award className='h-6 w-6' />;
    case 'Concerning':
      return <XCircle className='h-6 w-6' />;
    case 'Adequate':
      return <Hourglass className='h-6 w-6' />;
    default:
      return <Clock className='h-6 w-6' />;
  }
};

export const getRiskBadgeColor = (risk: string) => {
  switch (risk) {
    case 'Low':
      return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-300 dark:from-green-950/30 dark:to-emerald-950/30 dark:text-green-400 dark:border-green-700';
    case 'Medium':
      return 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border-yellow-300 dark:from-yellow-950/30 dark:to-amber-950/30 dark:text-yellow-400 dark:border-yellow-700';
    case 'High':
      return 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border-red-300 dark:from-red-950/30 dark:to-rose-950/30 dark:text-red-400 dark:border-red-700';
    default:
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-300 dark:from-gray-950/30 dark:to-slate-950/30 dark:text-gray-400 dark:border-gray-700';
  }
};

export function parseEvaluation(text: string): EvaluationReportData | null {
  try {
    const nameMatch = text.match(/## Candidate Analysis: (.+)/);
    const candidateName = nameMatch ? nameMatch[1] : 'Unknown Candidate';

    const authMatch = text.match(/\*Authenticity:\* (\d+)\/10/);
    const authenticity = authMatch ? Number.parseInt(authMatch[1]) : 0;

    const riskMatch = text.match(/\*AI Risk:\* (Low|Medium|High)/);
    const aiRisk = (riskMatch ? riskMatch[1] : 'Low') as 'Low' | 'Medium' | 'High';

    const tldrSection = text.match(/> \*TL;DR:\*([\s\S]*?)### SWOT ANALYSIS/);
    const tldr = tldrSection
      ? tldrSection[1]
          .split('\n')
          .filter((line) => line.trim().startsWith('> -'))
          .map((line) => line.replace(/> -/, '').trim())
      : [];

    const swotSectionMatch = text.match(/### SWOT ANALYSIS/);
    const swotSection = swotSectionMatch ? swotSectionMatch[0] : '';

    const parseSwotItems = (content: string): Array<{ title: string; description: string }> => {
      return content
        .split('\n')
        .filter((line) => line.trim().startsWith('- *[') || line.trim().startsWith('*['))
        .map((line) => {
          const cleanLine = line.replace(/^\s*-\s*/, '').trim();
          const match = cleanLine.match(/\*\[([^\]]+)\]:\*\s*(.+)/);
          return match
            ? { title: match[1], description: match[2] }
            : { title: 'Unknown', description: cleanLine };
        })
        .filter((item) => item.title !== 'Unknown');
    };

    const strengthsMatch = text.match(/\*Strengths:\*([\s\S]*?)\*Weaknesses:/);
    const strengths = strengthsMatch ? parseSwotItems(strengthsMatch[1]) : [];

    const weaknessesMatch = text.match(/\*Weaknesses:\*([\s\S]*?)\*Opportunities:/);
    const weaknesses = weaknessesMatch ? parseSwotItems(weaknessesMatch[1]) : [];

    const opportunitiesMatch = text.match(/\*Opportunities:\*([\s\S]*?)\*Threats:/);
    const opportunities = opportunitiesMatch ? parseSwotItems(opportunitiesMatch[1]) : [];

    const threatsMatch = text.match(/\*Threats:\*([\s\S]*?)### Defining Moments/);
    const threats = threatsMatch ? parseSwotItems(threatsMatch[1]) : [];

    const momentsSection = text.match(/### Defining Moments([\s\S]*?)\*Verdict:/);
    const definingMoments = momentsSection
      ? momentsSection[1]
          .split('\n')
          .filter((line) => line.trim().match(/^\d+\./))
          .map((line) => {
            const match = line.match(/\d+\.\s*\*\[([^\]]+)\]:\*\s*(.+)/);
            return match
              ? { title: match[1], description: match[2] }
              : { title: 'Unknown', description: line };
          })
      : [];

    const verdictMatch = text.match(/\*Verdict: ([^*]+)\*\s*-\s*(.+)/);
    const verdict = verdictMatch
      ? {
          status: verdictMatch[1] as 'Strong' | 'Adequate' | 'Concerning' | 'Needs Review',
          description: verdictMatch[2],
        }
      : {
          status: 'Needs Review' as const,
          description: 'Unable to determine verdict from evaluation',
        };

    return {
      candidateName,
      authenticity,
      aiRisk,
      tldr,
      swotSection,
      strengths,
      weaknesses,
      opportunities,
      threats,
      definingMoments,
      verdict,
    };
  } catch {
    return null;
  }
}
