import { AnalysisReport } from './analysis-report';
import { Card, CardContent } from '@/components/ui/card';
import { FileText } from 'lucide-react';

interface Props {
  evaluationData: string | null;
  isEvaluationLoading: boolean;
}

export const EvaluationReport = ({ evaluationData, isEvaluationLoading }: Props) => {
  console.log('🚀 ~ evaluationData:', evaluationData);

  return (
    <div className='h-full overflow-auto p-6'>
      <Card className='h-evaluation-template bg-card overflow-auto border-0 p-0'>
        <CardContent className='space-y-6 p-6'>
          {isEvaluationLoading ? (
            <div className='space-y-4'>
              <div className='h-6 w-3/4 animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-full animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-5/6 animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-4/5 animate-pulse rounded bg-gray-200' />
            </div>
          ) : evaluationData && evaluationData.trim() ? (
            <AnalysisReport rawText={`hello world`} />
          ) : (
            <div className='flex h-full min-h-[400px] items-center justify-center'>
              <div className='text-center'>
                <div className='mb-2 text-gray-400'>
                  <FileText className='mx-auto size-12' />
                </div>
                <h3 className='text-lg font-medium text-gray-900'>No Evaluation Available</h3>
                <p className='mt-1 text-sm text-gray-500'>
                  The overall evaluation comment has not been generated yet.
                  <br />
                  Or the candidate did not attend the interview.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
