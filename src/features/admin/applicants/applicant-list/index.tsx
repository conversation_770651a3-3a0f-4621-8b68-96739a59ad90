import { applicantColumns } from './columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { PageHeader } from '@/components/shared/PageHeader';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { useApplicantsQuery } from '@/hooks/api/use-applicant';
import { useTableState } from '@/hooks/use-table-state';
import { useNavigate } from '@tanstack/react-router';
import { useMemo } from 'react';

export default function ApplicantList() {
  const navigate = useNavigate();
  //   const [search, setSearch] = useState('');
  //   const debouncedSearch = useDebounce(search, 400);

  //   const { currentPageNumber, currentPageSize } = usePagination({});

  //   const offset = (currentPageNumber - 1) * currentPageSize;

  const { search, setSearch, debouncedSearch, pagination } = useTableState({
    initialFilters: {
      status: 'all',
    },
  });

  const { data: applicants, isLoading } = useApplicantsQuery(
    { limit: pagination.pageSize, offset: pagination.offset, search: debouncedSearch },
    { enabled: true }
  );

  const columns = useMemo(() => applicantColumns, []);

  return (
    <div className='bg-custom-white min-h-screen p-0'>
      <PageHeader title='Applicants List' />
      <div className='p-6 pt-0'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex items-center gap-3'>
                <SearchInput value={search} onChange={setSearch} />
              </div>
            </DataTableHeader.Left>
          </DataTableHeader>
          <DataTable
            data={applicants?.items || []}
            columns={columns}
            isLoading={isLoading}
            onRowClick={(row) => navigate({ to: `/admin/applicants/${row.id}` })}
          />
          <DataTableFooter>
            <TablePagination totalRecords={applicants?.total || 0} />
          </DataTableFooter>
        </DataTableContainer>
      </div>
    </div>
  );
}
