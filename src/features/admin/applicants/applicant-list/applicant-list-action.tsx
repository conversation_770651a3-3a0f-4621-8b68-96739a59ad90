import { ConfirmationModal } from '@/components/shared/confirmation-modal';
import { SimpleDropdown } from '@/components/ui';
import { useDeleteApplicantMutation } from '@/hooks/api/use-applicant';
import { useResendInvitationMutation } from '@/hooks/api/use-interview';
import { Forward, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Props {
  applicant: Applicant;
  jobId: string;
}

export const ApplicantListAction = ({ applicant, jobId }: Props) => {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const { mutate: resendInvitation, isPending } = useResendInvitationMutation();
  const { mutate: deleteApplicant, isPending: isDeleting } = useDeleteApplicantMutation();

  const handleResendInvitation = () => {
    if (!jobId) return;

    resendInvitation({ candidate_email: applicant.email, job_id: jobId! });
  };

  const handleRemoveClick = () => {
    setIsConfirmModalOpen(true);
  };

  const handleDeleteApplicant = async () => {
    await deleteApplicant(applicant.id);
    setIsConfirmModalOpen(false);
  };

  return (
    <>
      <SimpleDropdown
        items={[
          {
            label: 'Resend Link',
            onClick: handleResendInvitation,
            icon: <Forward />,
            disabled: isPending,
          },
          {
            label: 'Remove',
            labelClassName: 'text-red-500',
            onClick: handleRemoveClick,
            icon: <Trash2 className='text-red-400' />,
            disabled: isDeleting,
          },
        ]}
      />
      <ConfirmationModal
        title={`Remove Applicant`}
        description={`Are you sure you want to remove ${applicant.full_name} from applicants? This action cannot be undone.`}
        onConfirm={handleDeleteApplicant}
        open={isConfirmModalOpen}
        onOpenChange={setIsConfirmModalOpen}
      />
    </>
  );
};
