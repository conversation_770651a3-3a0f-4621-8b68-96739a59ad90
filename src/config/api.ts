export const API_ROUTES = {
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH_TOKEN: '/auth/refresh-token',
  },

  USER: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: 'user/update-profile',
  },

  INTERVIEW: {
    CREATE_FLOW: '/api/v1/interview-configs',
    GET_ALL_FLOWS: (limit: number, offset: number) =>
      `/api/v1/interview-configs?limit=${limit}&offset=${offset}`,
    PREPARE_INTERVIEW: '/interviews/prepare',
    CREATE_USER_INTERVIEW: '/interviews',
    RUN_EVALUATION: (interviewId: string) => `/interview-evaluations/${interviewId}/generate`,
    COMPLETE_INTERVIEW: (interviewId: string) => `/interviews/${interviewId}/complete`,
    SUBMIT_FEEDBACK: (userInterviewId: string) => `/api/v1/interviews/${userInterviewId}/feedback`,
    GET_CANDIDATES_BY_JOB: (
      jobId: string,
      limit: number,
      offset: number,
      status: string | undefined,
      searchParam?: string
    ) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates?limit=${limit}&offset=${offset}&status=${status}${searchParam}`,
    GET_CANDIDATE_EVALUATION: (jobId: string, candidateId: string) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates/${candidateId}/evaluation`,
    GET_CANDIDATE_COUNT: (jobId: string) => `/api/v1/candidates/count/${jobId}`,
    GET_DASHBOARD_JOBS: '/api/v1/dashboards/jobs',
    RESEND_INVITATION: '/interviews/resend-invitation',
  },

  PROMPT: {
    CREATE: '/api/v1/agents',
    GET_ALL: '/api/v1/agents',
    GET_BY_ID: (agentId: string) => `/api/v1/agents/${agentId}`,
    GENERATE: '/api/v1/agents/generate-prompt',
    LIVE_PREVIEW: '/live-interview/next-question',
    LIVE_EVALUATION: '/api/v1/live-evaluation',
    PREVIEW_CV: (cvLink: string) => `/api/v1/candidates/preview-cv?cv_link=${cvLink}`,
  },

  JOB: {
    CREATE: '/jobs',
    GET_ALL: '/jobs',
    GET_ACTIVE_JOBS: '/jobs/active',
    GET_JOB_ROLES: '/job-roles',
    GET_DETAILS: (jobId: string) => `/jobs/${jobId}/details`,
    GET_BY_ID: (jobId: string) => `/jobs/${jobId}`,
    GET_GENERATE_DESCRIPTION: (jobId: string) => `/jobs/${jobId}/generate-description`,
    GET_DETAILS_FOR_ADMIN: (jobId: string) => `/jobs/${jobId}`,
    LIST_CANDIDATE: (jobId: string) => `/jobs/${jobId}/candidates`,
    DELETE: (jobId: string) => `/jobs/${jobId}`,
  },

  CANDIDATE: {
    CREATE: '/candidates',
    VERIFY: '/candidates/verify',
    UPLOAD_CV: (jobId: string) => `/jobs/${jobId}/candidates/upload-cv`,
    APPLY_JOB: (jobId: string) => `/jobs/${jobId}/candidates`,
    VERIFY_EMAIL: '/candidates/verify-email',
    RESEND_OTP: '/candidates/resend-otp',
    COMPLETE_PROFILE: (candidateId: string) => `/candidates/${candidateId}/complete`,
  },
  APPLICANT: {
    GET_ALL: '/candidates',
    GET_DETAILS: (applicantId: string) => `/candidates/${applicantId}`,
    PREVIEW_CV: (cvLink: string) => `/candidates/preview-cv?cv_link=${cvLink}`,
    DELETE: (applicantId: string) => `/candidates/${applicantId}`,
  },

  STT: {
    TRANSCRIBE_SYNC: '/transcribe/sync',
  },
  QUESTION: {
    GENERATE: '/question-engine/generate-question/sync',
    GENERATE_SYNC: '/question-engine/generate-question/sync',
    UPDATE_ANSWER: '/question-engine/update-answer',
  },
  PERSONA: {
    GET_ALL: '/personas',
    ATTACH_TO_JOB: (jobId: string) => `/jobs/${jobId}/personas/batch`,
    GET_JOB_PERSONAS: (jobId: string) => `/jobs/${jobId}/personas`,
  },
};
