interface Persona {
  id: string;
  title: string;
  description: string;
  prompt_text: string;
  prompt_generation_metadata: Record<string, unknown>;
  created_by: string;
  created_at: string;
}

interface JobPersona {
  persona_id: string;
  time_duration: number;
  execution_order: number;
}

interface AttachJobPersona {
  id: string;
  job_id: string;
  persona_title: string;
  persona_id: string;
  time_duration: number;
  execution_order: number;
}
