/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authLoginImport } from './routes/(auth)/login'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedAdminRouteImport } from './routes/_authenticated/admin/route'
import { Route as AuthenticatedSettingsIndexImport } from './routes/_authenticated/settings/index'
import { Route as interviewInterviewIndexImport } from './routes/(interview)/interview/index'
import { Route as interviewInterviewOnboardingIndexImport } from './routes/(interview)/interview-onboarding/index'
import { Route as interviewInterviewOnboardingSystemSetupIndexImport } from './routes/(interview)/interview-onboarding-system-setup/index'
import { Route as interviewInterviewCompleteIndexImport } from './routes/(interview)/interview-complete/index'
import { Route as candidateJobListIndexImport } from './routes/(candidate)/job-list/index'
import { Route as AuthenticatedSettingsNotificationsImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedAdminJobsIndexImport } from './routes/_authenticated/admin/jobs/index'
import { Route as AuthenticatedAdminInterviewAgentIndexImport } from './routes/_authenticated/admin/interview-agent/index'
import { Route as AuthenticatedAdminDashboardIndexImport } from './routes/_authenticated/admin/dashboard/index'
import { Route as AuthenticatedAdminApplicantsIndexImport } from './routes/_authenticated/admin/applicants/index'
import { Route as candidateJobApplicationJobIdIndexImport } from './routes/(candidate)/job-application/$jobId/index'
import { Route as AuthenticatedAdminJobsEditIndexImport } from './routes/_authenticated/admin/jobs/edit/index'
import { Route as AuthenticatedAdminJobsCreateIndexImport } from './routes/_authenticated/admin/jobs/create/index'
import { Route as AuthenticatedAdminJobsJobIdIndexImport } from './routes/_authenticated/admin/jobs/$jobId/index'
import { Route as AuthenticatedAdminApplicantsApplicantIdIndexImport } from './routes/_authenticated/admin/applicants/$applicantId/index'

// Create/Update Routes

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authLoginRoute = authLoginImport.update({
  id: '/(auth)/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedSettingsRouteRoute = AuthenticatedSettingsRouteImport.update(
  {
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any,
)

const AuthenticatedAdminRouteRoute = AuthenticatedAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSettingsIndexRoute = AuthenticatedSettingsIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any,
)

const interviewInterviewIndexRoute = interviewInterviewIndexImport.update({
  id: '/(interview)/interview/',
  path: '/interview/',
  getParentRoute: () => rootRoute,
} as any)

const interviewInterviewOnboardingIndexRoute =
  interviewInterviewOnboardingIndexImport.update({
    id: '/(interview)/interview-onboarding/',
    path: '/interview-onboarding/',
    getParentRoute: () => rootRoute,
  } as any)

const interviewInterviewOnboardingSystemSetupIndexRoute =
  interviewInterviewOnboardingSystemSetupIndexImport.update({
    id: '/(interview)/interview-onboarding-system-setup/',
    path: '/interview-onboarding-system-setup/',
    getParentRoute: () => rootRoute,
  } as any)

const interviewInterviewCompleteIndexRoute =
  interviewInterviewCompleteIndexImport.update({
    id: '/(interview)/interview-complete/',
    path: '/interview-complete/',
    getParentRoute: () => rootRoute,
  } as any)

const candidateJobListIndexRoute = candidateJobListIndexImport.update({
  id: '/(candidate)/job-list/',
  path: '/job-list/',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)

const AuthenticatedAdminJobsIndexRoute =
  AuthenticatedAdminJobsIndexImport.update({
    id: '/jobs/',
    path: '/jobs/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminInterviewAgentIndexRoute =
  AuthenticatedAdminInterviewAgentIndexImport.update({
    id: '/interview-agent/',
    path: '/interview-agent/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminDashboardIndexRoute =
  AuthenticatedAdminDashboardIndexImport.update({
    id: '/dashboard/',
    path: '/dashboard/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminApplicantsIndexRoute =
  AuthenticatedAdminApplicantsIndexImport.update({
    id: '/applicants/',
    path: '/applicants/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const candidateJobApplicationJobIdIndexRoute =
  candidateJobApplicationJobIdIndexImport.update({
    id: '/(candidate)/job-application/$jobId/',
    path: '/job-application/$jobId/',
    getParentRoute: () => rootRoute,
  } as any)

const AuthenticatedAdminJobsEditIndexRoute =
  AuthenticatedAdminJobsEditIndexImport.update({
    id: '/jobs/edit/',
    path: '/jobs/edit/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminJobsCreateIndexRoute =
  AuthenticatedAdminJobsCreateIndexImport.update({
    id: '/jobs/create/',
    path: '/jobs/create/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminJobsJobIdIndexRoute =
  AuthenticatedAdminJobsJobIdIndexImport.update({
    id: '/jobs/$jobId/',
    path: '/jobs/$jobId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminApplicantsApplicantIdIndexRoute =
  AuthenticatedAdminApplicantsApplicantIdIndexImport.update({
    id: '/applicants/$applicantId/',
    path: '/applicants/$applicantId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/admin': {
      id: '/_authenticated/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthenticatedAdminRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/login': {
      id: '/(auth)/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof authLoginImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/(candidate)/job-list/': {
      id: '/(candidate)/job-list/'
      path: '/job-list'
      fullPath: '/job-list'
      preLoaderRoute: typeof candidateJobListIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-complete/': {
      id: '/(interview)/interview-complete/'
      path: '/interview-complete'
      fullPath: '/interview-complete'
      preLoaderRoute: typeof interviewInterviewCompleteIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-onboarding-system-setup/': {
      id: '/(interview)/interview-onboarding-system-setup/'
      path: '/interview-onboarding-system-setup'
      fullPath: '/interview-onboarding-system-setup'
      preLoaderRoute: typeof interviewInterviewOnboardingSystemSetupIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-onboarding/': {
      id: '/(interview)/interview-onboarding/'
      path: '/interview-onboarding'
      fullPath: '/interview-onboarding'
      preLoaderRoute: typeof interviewInterviewOnboardingIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview/': {
      id: '/(interview)/interview/'
      path: '/interview'
      fullPath: '/interview'
      preLoaderRoute: typeof interviewInterviewIndexImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexImport
      parentRoute: typeof AuthenticatedSettingsRouteImport
    }
    '/(candidate)/job-application/$jobId/': {
      id: '/(candidate)/job-application/$jobId/'
      path: '/job-application/$jobId'
      fullPath: '/job-application/$jobId'
      preLoaderRoute: typeof candidateJobApplicationJobIdIndexImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/admin/applicants/': {
      id: '/_authenticated/admin/applicants/'
      path: '/applicants'
      fullPath: '/admin/applicants'
      preLoaderRoute: typeof AuthenticatedAdminApplicantsIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/dashboard/': {
      id: '/_authenticated/admin/dashboard/'
      path: '/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AuthenticatedAdminDashboardIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/interview-agent/': {
      id: '/_authenticated/admin/interview-agent/'
      path: '/interview-agent'
      fullPath: '/admin/interview-agent'
      preLoaderRoute: typeof AuthenticatedAdminInterviewAgentIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/': {
      id: '/_authenticated/admin/jobs/'
      path: '/jobs'
      fullPath: '/admin/jobs'
      preLoaderRoute: typeof AuthenticatedAdminJobsIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/applicants/$applicantId/': {
      id: '/_authenticated/admin/applicants/$applicantId/'
      path: '/applicants/$applicantId'
      fullPath: '/admin/applicants/$applicantId'
      preLoaderRoute: typeof AuthenticatedAdminApplicantsApplicantIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/$jobId/': {
      id: '/_authenticated/admin/jobs/$jobId/'
      path: '/jobs/$jobId'
      fullPath: '/admin/jobs/$jobId'
      preLoaderRoute: typeof AuthenticatedAdminJobsJobIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/create/': {
      id: '/_authenticated/admin/jobs/create/'
      path: '/jobs/create'
      fullPath: '/admin/jobs/create'
      preLoaderRoute: typeof AuthenticatedAdminJobsCreateIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/edit/': {
      id: '/_authenticated/admin/jobs/edit/'
      path: '/jobs/edit'
      fullPath: '/admin/jobs/edit'
      preLoaderRoute: typeof AuthenticatedAdminJobsEditIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedAdminRouteRouteChildren {
  AuthenticatedAdminApplicantsIndexRoute: typeof AuthenticatedAdminApplicantsIndexRoute
  AuthenticatedAdminDashboardIndexRoute: typeof AuthenticatedAdminDashboardIndexRoute
  AuthenticatedAdminInterviewAgentIndexRoute: typeof AuthenticatedAdminInterviewAgentIndexRoute
  AuthenticatedAdminJobsIndexRoute: typeof AuthenticatedAdminJobsIndexRoute
  AuthenticatedAdminApplicantsApplicantIdIndexRoute: typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  AuthenticatedAdminJobsJobIdIndexRoute: typeof AuthenticatedAdminJobsJobIdIndexRoute
  AuthenticatedAdminJobsCreateIndexRoute: typeof AuthenticatedAdminJobsCreateIndexRoute
  AuthenticatedAdminJobsEditIndexRoute: typeof AuthenticatedAdminJobsEditIndexRoute
}

const AuthenticatedAdminRouteRouteChildren: AuthenticatedAdminRouteRouteChildren =
  {
    AuthenticatedAdminApplicantsIndexRoute:
      AuthenticatedAdminApplicantsIndexRoute,
    AuthenticatedAdminDashboardIndexRoute:
      AuthenticatedAdminDashboardIndexRoute,
    AuthenticatedAdminInterviewAgentIndexRoute:
      AuthenticatedAdminInterviewAgentIndexRoute,
    AuthenticatedAdminJobsIndexRoute: AuthenticatedAdminJobsIndexRoute,
    AuthenticatedAdminApplicantsApplicantIdIndexRoute:
      AuthenticatedAdminApplicantsApplicantIdIndexRoute,
    AuthenticatedAdminJobsJobIdIndexRoute:
      AuthenticatedAdminJobsJobIdIndexRoute,
    AuthenticatedAdminJobsCreateIndexRoute:
      AuthenticatedAdminJobsCreateIndexRoute,
    AuthenticatedAdminJobsEditIndexRoute: AuthenticatedAdminJobsEditIndexRoute,
  }

const AuthenticatedAdminRouteRouteWithChildren =
  AuthenticatedAdminRouteRoute._addFileChildren(
    AuthenticatedAdminRouteRouteChildren,
  )

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedAdminRouteRoute: typeof AuthenticatedAdminRouteRouteWithChildren
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedAdminRouteRoute: AuthenticatedAdminRouteRouteWithChildren,
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/login': typeof authLoginRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/job-list': typeof candidateJobListIndexRoute
  '/interview-complete': typeof interviewInterviewCompleteIndexRoute
  '/interview-onboarding-system-setup': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/interview-onboarding': typeof interviewInterviewOnboardingIndexRoute
  '/interview': typeof interviewInterviewIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/job-application/$jobId': typeof candidateJobApplicationJobIdIndexRoute
  '/admin/applicants': typeof AuthenticatedAdminApplicantsIndexRoute
  '/admin/dashboard': typeof AuthenticatedAdminDashboardIndexRoute
  '/admin/interview-agent': typeof AuthenticatedAdminInterviewAgentIndexRoute
  '/admin/jobs': typeof AuthenticatedAdminJobsIndexRoute
  '/admin/applicants/$applicantId': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/admin/jobs/$jobId': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/admin/jobs/create': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/admin/jobs/edit': typeof AuthenticatedAdminJobsEditIndexRoute
}

export interface FileRoutesByTo {
  '/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/login': typeof authLoginRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/job-list': typeof candidateJobListIndexRoute
  '/interview-complete': typeof interviewInterviewCompleteIndexRoute
  '/interview-onboarding-system-setup': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/interview-onboarding': typeof interviewInterviewOnboardingIndexRoute
  '/interview': typeof interviewInterviewIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/job-application/$jobId': typeof candidateJobApplicationJobIdIndexRoute
  '/admin/applicants': typeof AuthenticatedAdminApplicantsIndexRoute
  '/admin/dashboard': typeof AuthenticatedAdminDashboardIndexRoute
  '/admin/interview-agent': typeof AuthenticatedAdminInterviewAgentIndexRoute
  '/admin/jobs': typeof AuthenticatedAdminJobsIndexRoute
  '/admin/applicants/$applicantId': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/admin/jobs/$jobId': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/admin/jobs/create': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/admin/jobs/edit': typeof AuthenticatedAdminJobsEditIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/(auth)/login': typeof authLoginRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/(candidate)/job-list/': typeof candidateJobListIndexRoute
  '/(interview)/interview-complete/': typeof interviewInterviewCompleteIndexRoute
  '/(interview)/interview-onboarding-system-setup/': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/(interview)/interview-onboarding/': typeof interviewInterviewOnboardingIndexRoute
  '/(interview)/interview/': typeof interviewInterviewIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/(candidate)/job-application/$jobId/': typeof candidateJobApplicationJobIdIndexRoute
  '/_authenticated/admin/applicants/': typeof AuthenticatedAdminApplicantsIndexRoute
  '/_authenticated/admin/dashboard/': typeof AuthenticatedAdminDashboardIndexRoute
  '/_authenticated/admin/interview-agent/': typeof AuthenticatedAdminInterviewAgentIndexRoute
  '/_authenticated/admin/jobs/': typeof AuthenticatedAdminJobsIndexRoute
  '/_authenticated/admin/applicants/$applicantId/': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/_authenticated/admin/jobs/$jobId/': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/_authenticated/admin/jobs/create/': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/_authenticated/admin/jobs/edit/': typeof AuthenticatedAdminJobsEditIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/admin'
    | '/settings'
    | '/login'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/job-list'
    | '/interview-complete'
    | '/interview-onboarding-system-setup'
    | '/interview-onboarding'
    | '/interview'
    | '/settings/'
    | '/job-application/$jobId'
    | '/admin/applicants'
    | '/admin/dashboard'
    | '/admin/interview-agent'
    | '/admin/jobs'
    | '/admin/applicants/$applicantId'
    | '/admin/jobs/$jobId'
    | '/admin/jobs/create'
    | '/admin/jobs/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/admin'
    | '/login'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/job-list'
    | '/interview-complete'
    | '/interview-onboarding-system-setup'
    | '/interview-onboarding'
    | '/interview'
    | '/settings'
    | '/job-application/$jobId'
    | '/admin/applicants'
    | '/admin/dashboard'
    | '/admin/interview-agent'
    | '/admin/jobs'
    | '/admin/applicants/$applicantId'
    | '/admin/jobs/$jobId'
    | '/admin/jobs/create'
    | '/admin/jobs/edit'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/admin'
    | '/_authenticated/settings'
    | '/(auth)/login'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/(candidate)/job-list/'
    | '/(interview)/interview-complete/'
    | '/(interview)/interview-onboarding-system-setup/'
    | '/(interview)/interview-onboarding/'
    | '/(interview)/interview/'
    | '/_authenticated/settings/'
    | '/(candidate)/job-application/$jobId/'
    | '/_authenticated/admin/applicants/'
    | '/_authenticated/admin/dashboard/'
    | '/_authenticated/admin/interview-agent/'
    | '/_authenticated/admin/jobs/'
    | '/_authenticated/admin/applicants/$applicantId/'
    | '/_authenticated/admin/jobs/$jobId/'
    | '/_authenticated/admin/jobs/create/'
    | '/_authenticated/admin/jobs/edit/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authLoginRoute: typeof authLoginRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
  candidateJobListIndexRoute: typeof candidateJobListIndexRoute
  interviewInterviewCompleteIndexRoute: typeof interviewInterviewCompleteIndexRoute
  interviewInterviewOnboardingSystemSetupIndexRoute: typeof interviewInterviewOnboardingSystemSetupIndexRoute
  interviewInterviewOnboardingIndexRoute: typeof interviewInterviewOnboardingIndexRoute
  interviewInterviewIndexRoute: typeof interviewInterviewIndexRoute
  candidateJobApplicationJobIdIndexRoute: typeof candidateJobApplicationJobIdIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authLoginRoute: authLoginRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
  candidateJobListIndexRoute: candidateJobListIndexRoute,
  interviewInterviewCompleteIndexRoute: interviewInterviewCompleteIndexRoute,
  interviewInterviewOnboardingSystemSetupIndexRoute:
    interviewInterviewOnboardingSystemSetupIndexRoute,
  interviewInterviewOnboardingIndexRoute:
    interviewInterviewOnboardingIndexRoute,
  interviewInterviewIndexRoute: interviewInterviewIndexRoute,
  candidateJobApplicationJobIdIndexRoute:
    candidateJobApplicationJobIdIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/(auth)/login",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503",
        "/(candidate)/job-list/",
        "/(interview)/interview-complete/",
        "/(interview)/interview-onboarding-system-setup/",
        "/(interview)/interview-onboarding/",
        "/(interview)/interview/",
        "/(candidate)/job-application/$jobId/"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/admin",
        "/_authenticated/settings",
        "/_authenticated/"
      ]
    },
    "/_authenticated/admin": {
      "filePath": "_authenticated/admin/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/admin/applicants/",
        "/_authenticated/admin/dashboard/",
        "/_authenticated/admin/interview-agent/",
        "/_authenticated/admin/jobs/",
        "/_authenticated/admin/applicants/$applicantId/",
        "/_authenticated/admin/jobs/$jobId/",
        "/_authenticated/admin/jobs/create/",
        "/_authenticated/admin/jobs/edit/"
      ]
    },
    "/_authenticated/settings": {
      "filePath": "_authenticated/settings/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/settings/account",
        "/_authenticated/settings/appearance",
        "/_authenticated/settings/display",
        "/_authenticated/settings/notifications",
        "/_authenticated/settings/"
      ]
    },
    "/(auth)/login": {
      "filePath": "(auth)/login.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/account": {
      "filePath": "_authenticated/settings/account.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/appearance": {
      "filePath": "_authenticated/settings/appearance.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/display": {
      "filePath": "_authenticated/settings/display.tsx",
      "parent": "/_authenticated/settings"
    },
    "/_authenticated/settings/notifications": {
      "filePath": "_authenticated/settings/notifications.tsx",
      "parent": "/_authenticated/settings"
    },
    "/(candidate)/job-list/": {
      "filePath": "(candidate)/job-list/index.tsx"
    },
    "/(interview)/interview-complete/": {
      "filePath": "(interview)/interview-complete/index.tsx"
    },
    "/(interview)/interview-onboarding-system-setup/": {
      "filePath": "(interview)/interview-onboarding-system-setup/index.tsx"
    },
    "/(interview)/interview-onboarding/": {
      "filePath": "(interview)/interview-onboarding/index.tsx"
    },
    "/(interview)/interview/": {
      "filePath": "(interview)/interview/index.tsx"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.tsx",
      "parent": "/_authenticated/settings"
    },
    "/(candidate)/job-application/$jobId/": {
      "filePath": "(candidate)/job-application/$jobId/index.tsx"
    },
    "/_authenticated/admin/applicants/": {
      "filePath": "_authenticated/admin/applicants/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/dashboard/": {
      "filePath": "_authenticated/admin/dashboard/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/interview-agent/": {
      "filePath": "_authenticated/admin/interview-agent/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/": {
      "filePath": "_authenticated/admin/jobs/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/applicants/$applicantId/": {
      "filePath": "_authenticated/admin/applicants/$applicantId/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/$jobId/": {
      "filePath": "_authenticated/admin/jobs/$jobId/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/create/": {
      "filePath": "_authenticated/admin/jobs/create/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/edit/": {
      "filePath": "_authenticated/admin/jobs/edit/index.tsx",
      "parent": "/_authenticated/admin"
    }
  }
}
ROUTE_MANIFEST_END */
