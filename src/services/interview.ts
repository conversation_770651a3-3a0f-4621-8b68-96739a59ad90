import { ApiServiceInstance, CandidateApiServiceInstance, QuestionsApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

const { LIVE_PREVIEW, LIVE_EVALUATION } = API_ROUTES.PROMPT;
const { CREATE_USER_INTERVIEW } = API_ROUTES.INTERVIEW;

export interface QuestionRequest {
  role: string;
  session_id?: string | undefined;
  cv_s3_key?: string | undefined;
  difficulty: string;
  prompt_text: string;
  qa_array: {
    question: string;
    answer: string;
  }[];
  remaining_time?: number;
}

export interface QuestionResponse {
  data: string;
}
export interface CandidateVerifyRequest {
  token: string;
}

export interface CandidateVerifyResponse {
  candidate_id: string;
}

export interface AnswerSubmissionRequest {
  interviewId: string;
  questionId: string;
  answer: string;
  duration: number;
  timestamp: string;
}

export interface InterviewCreationRequest {
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
  totalDuration: number;
}

export interface InterviewCreationResponse {
  interviewId: string;
  totalDuration: number;
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
}

export interface PrepareInterviewRequest {
  candidate_id: string;
  job_id?: string;
}

export interface InterviewItem {
  id: string;
  status: string;
  persona_id: string;
  execution_order: number;
  time_duration: number;
}

export interface PrepareInterviewResponse {
  candidate_id: string;
  job_id: string;
  interview_items: InterviewItem[];
}

export interface EvaluationResponse {
  evaluation_data: string | null;
}

export interface GenerateQuestionSyncRequest {
  channel_id: string;
  is_interview_ending_question?: boolean;
}

export interface GenerateQuestionSyncResponse {
  type: string;
  channel_id: string;
  chat_id: string;
  content: string;
  audio_data: string;
  audio_length_in_milliseconds: number;
}

export interface CompleteInterviewRequest {
  interview_id: string;
}

export interface CompleteInterviewResponse {
  message: string;
  interview_id: string;
  status: string;
}

export interface UpdateAnswerRequest {
  channel_id: string | null;
  chat_id: string | null;
  answer: string;
}

export interface UpdateAnswerResponse {
  success: boolean;
  message?: string;
}

export const verifyCandidate = async (
  payload: CandidateVerifyRequest
): Promise<IResponseData<CandidateVerifyResponse>> => {
  return CandidateApiServiceInstance.callPostApi<CandidateVerifyResponse, CandidateVerifyRequest>(
    API_ROUTES.CANDIDATE.VERIFY,
    payload
  );
};

// Generate next question (no auth required)
export const generateQuestion = async (
  payload: QuestionRequest
): Promise<IResponseData<QuestionResponse>> => {
  return CandidateApiServiceInstance.callPostApi<QuestionResponse, QuestionRequest>(
    LIVE_PREVIEW,
    payload
  );
};

// Submit answer (no auth required)
export const submitAnswer = async (
  payload: AnswerSubmissionRequest
): Promise<IResponseData<unknown>> => {
  return ApiServiceInstance.callPostApi<unknown, AnswerSubmissionRequest>(LIVE_EVALUATION, payload);
};

// Create interview session
export const createInterviewSession = async (
  payload: InterviewCreationRequest
): Promise<IResponseData<InterviewCreationResponse>> => {
  return ApiServiceInstance.callPostApi<InterviewCreationResponse, InterviewCreationRequest>(
    CREATE_USER_INTERVIEW,
    payload
  );
};

// Prepare interview session (no auth required)
export const prepareInterview = async (
  payload: PrepareInterviewRequest
): Promise<IResponseData<PrepareInterviewResponse>> => {
  return CandidateApiServiceInstance.callPostApi<PrepareInterviewResponse, PrepareInterviewRequest>(
    API_ROUTES.INTERVIEW.PREPARE_INTERVIEW,
    payload
  );
};

// Run evaluation for interview
export const runEvaluation = async (
  interviewId: string
): Promise<IResponseData<EvaluationResponse>> => {
  return CandidateApiServiceInstance.callPostApi<EvaluationResponse, { interview_id: string }>(
    API_ROUTES.INTERVIEW.RUN_EVALUATION(interviewId),
    { interview_id: interviewId }
  );
};

// Generate question sync (text + audio response)
export const generateQuestionSync = async (
  payload: GenerateQuestionSyncRequest
): Promise<IResponseData<GenerateQuestionSyncResponse>> => {
  return QuestionsApiServiceInstance.callPostApi<
    GenerateQuestionSyncResponse,
    GenerateQuestionSyncRequest
  >(API_ROUTES.QUESTION.GENERATE_SYNC, payload);
};

// Complete interview session
export const completeInterview = async (
  interviewId: string
): Promise<IResponseData<CompleteInterviewResponse>> => {
  return CandidateApiServiceInstance.callPatchApi<
    CompleteInterviewResponse,
    CompleteInterviewRequest
  >(API_ROUTES.INTERVIEW.COMPLETE_INTERVIEW(interviewId), { interview_id: interviewId });
};

// Update answer
export const updateAnswer = async (
  payload: UpdateAnswerRequest
): Promise<IResponseData<UpdateAnswerResponse>> => {
  return QuestionsApiServiceInstance.callPutApi<UpdateAnswerResponse, UpdateAnswerRequest>(
    API_ROUTES.QUESTION.UPDATE_ANSWER,
    payload
  );
};

export const resendInvitation = async (payload: {
  candidate_email: string;
  job_id: string;
}): Promise<IResponseData<string>> => {
  return ApiServiceInstance.callPostApi<string, { candidate_email: string; job_id: string }>(
    API_ROUTES.INTERVIEW.RESEND_INVITATION,
    payload
  );
};
