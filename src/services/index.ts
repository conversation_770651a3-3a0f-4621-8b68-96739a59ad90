import { buildQueryParams } from '@/utils/helper';
import axios, { AxiosError, AxiosInstance } from 'axios';
import Cookies from 'js-cookie';

class ApiService {
  baseUrl: string;
  instance: AxiosInstance;
  private skipAuth: boolean;

  constructor(baseUrl: string, skipAuth: boolean = false) {
    this.baseUrl = baseUrl;
    this.skipAuth = skipAuth;
    this.instance = axios.create({
      baseURL: baseUrl,
      timeout: 200000,
      validateStatus(status: number) {
        return (status >= 200 && status < 300) || status === 404;
      },
    });

    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        if (!this.skipAuth) {
          const token = Cookies.get(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }
        return config;
      },
      (error: AxiosError) => {
        // console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // // Response interceptor
    // this.instance.interceptors.response.use(
    //   (response: AxiosResponse) => response,
    //   (error: AxiosError) => {
    //     if (error.response?.status === 401) {
    //       Cookies.remove(process.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);
    //       toast.error('Session expired. Please login again.');
    //       window.location.href = '/login';
    //     } else if (error.response?.status >= 500) {
    //       toast.error('Server error. Please try again later.');
    //     }
    //     return Promise.reject(error);
    //   }
    // );
  }

  // Helper to get headers
  getHeaders(authorization?: string | null, contentType?: string | null) {
    const headers: Record<string, string> = {
      'Content-Type': contentType || 'application/json',
    };

    if (!this.skipAuth) {
      const token = Cookies.get(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);
      headers.Authorization = authorization || (token ? `Bearer ${token}` : '');
    }

    return headers;
  }

  // GET API
  callGetApi<T>(
    url: string,
    authorization?: string | null,
    params?: Record<string, string | number>
  ): Promise<IResponseData<T>> {
    return this.instance.get(url, {
      headers: this.getHeaders(authorization),
      ...(Object.keys(params ?? {}).length > 0 && {
        params: buildQueryParams(params as QueryParams),
      }),
    });
  }

  // POST API
  callPostApi<T, R>(
    url: string,
    payload: R,
    authorization?: string | null,
    contentType?: string | null
  ): Promise<IResponseData<T>> {
    return this.instance.post(url, payload, {
      headers: this.getHeaders(authorization, contentType),
    });
  }

  // PUT API
  callPutApi<T, R>(
    url: string,
    payload: R,
    authorization?: string | null,
    contentType?: string | null
  ): Promise<IResponseData<T>> {
    return this.instance.put(url, payload, {
      headers: this.getHeaders(authorization, contentType),
    });
  }

  // PATCH API
  callPatchApi<T, R>(
    url: string,
    payload?: R,
    authorization?: string | null,
    contentType?: string | null
  ): Promise<IResponseData<T>> {
    return this.instance.patch(url, payload, {
      headers: this.getHeaders(authorization, contentType),
    });
  }

  // DELETE API
  callDeleteApi<T>(
    url: string,
    payload?: T,
    authorization?: string | null
  ): Promise<IResponseData<T>> {
    return this.instance.delete(url, {
      data: payload ?? undefined,
      headers: this.getHeaders(authorization),
    });
  }
}

// Create service instance
export const API_BASE_URL = import.meta.env.VITE_BASE_API_URL;
export const STT_API_URL = import.meta.env.VITE_PREVIA_STT_API_URL;
export const QUESTIONS_API_URL = import.meta.env.VITE_PREVIA_QUESTIONS_API_URL;

export const ApiServiceInstance = new ApiService(API_BASE_URL);

export const IdentityServiceInstance = new ApiService(import.meta.env.VITE_IDENTITY_API_URL);
export const CandidateApiServiceInstance = new ApiService(API_BASE_URL, true); // Skip auth for candidate APIs
export const STTServiceInstance = new ApiService(STT_API_URL, true); // Skip auth for STT APIs
export const QuestionsApiServiceInstance = new ApiService(QUESTIONS_API_URL, true); // Skip auth for Questions APIs
