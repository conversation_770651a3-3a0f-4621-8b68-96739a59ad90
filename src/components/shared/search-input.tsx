import { Input } from '../ui';
import { cn } from '@/lib/utils';
import { Search } from 'lucide-react';

interface Props extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  wrapperClassName?: string;
  onChange?: (value: string) => void;
}

export const SearchInput = ({ wrapperClassName, className, onChange, ...props }: Props) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div className={cn('relative', wrapperClassName)}>
      <Input
        className={cn('w-70 pl-10', className)}
        onChange={handleChange}
        {...props}
        placeholder='Search'
      />
      <Search className='text-gray-light absolute top-1/2 left-3 size-5 -translate-y-1/2' />
    </div>
  );
};
