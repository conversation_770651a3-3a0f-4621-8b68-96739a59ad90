'use client';

/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import { Minus, Plus } from 'lucide-react';
import type * as pdfjsLib from 'pdfjs-dist';
import { useState, useEffect, useRef, Fragment } from 'react';
import { toast } from 'sonner';

interface PreviewPdfProps {
  fileUrl?: string;
  isLoading?: boolean;
  isPreviewDataLoading?: boolean;
  scale?: number;
  className?: string;
  styles?: React.CSSProperties;
}

const PreviewPdf = ({
  fileUrl,
  isLoading = false,
  isPreviewDataLoading = false,
  scale: initialScale = 1,
  className = '',
  styles,
}: PreviewPdfProps) => {
  const pdfDocRef = useRef<any>(null);
  const [scale, setScale] = useState<number>(initialScale);
  const [pdfLoaded, setPdfLoaded] = useState<boolean>(false);
  const [pdfLib, setPdfLib] = useState<typeof pdfjsLib | null>(null);
  const renderTaskRef = useRef<any>(null);

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [startY, setStartY] = useState<number>(0);
  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [numPages, setNumPages] = useState<number>(0);
  const [renderedPages, setRenderedPages] = useState<HTMLCanvasElement[]>([]);

  useEffect(() => {
    const loadPdfLib = async () => {
      try {
        const pdfjsLib = await import('pdfjs-dist');
        // Set the worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;
        setPdfLib(pdfjsLib);
      } catch (error) {
        console.error('Error loading PDF.js:', error);
        toast.error('Failed to load PDF');
      }
    };

    void loadPdfLib();
  }, []);

  // Cleanup function to cancel any ongoing render tasks
  const cancelRenderTask = () => {
    if (renderTaskRef.current && renderTaskRef.current.cancel) {
      renderTaskRef.current.cancel();
      renderTaskRef.current = null;
    }
  };
  const renderAllPages = async (scale: number) => {
    if (!pdfDocRef.current) return;

    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      const page = await pdfDocRef.current.getPage(i);
      const viewport = page.getViewport({ scale: scale });

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      await page.render({
        canvasContext: context,
        viewport: viewport,
      }).promise;

      pages.push(canvas);
    }
    setRenderedPages(pages);
  };

  // Load PDF when URL is available and pdfLib is loaded
  useEffect(() => {
    // Cancel any previous render task
    cancelRenderTask();

    // Clear previous PDF document
    if (pdfDocRef.current) {
      pdfDocRef.current.destroy();
      pdfDocRef.current = null;
      setPdfLoaded(false);
    }

    if (fileUrl && pdfLib) {
      const loadingTask = pdfLib.getDocument(fileUrl);

      loadingTask.promise.then(
        (doc: any) => {
          pdfDocRef.current = doc;
          setNumPages(doc.numPages);

          setPdfLoaded(true);
          void renderAllPages(scale);
        },
        (err: any) => {
          console.error('Error loading PDF: ', err);
          toast.error('Failed to load PDF preview');
        }
      );
    }

    // Cleanup on unmount or when fileUrl changes
    return () => {
      cancelRenderTask();
      if (pdfDocRef.current) {
        pdfDocRef.current.destroy();
        pdfDocRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileUrl, pdfLib]);

  // Re-render page when scale changes
  useEffect(() => {
    if (pdfLoaded) {
      // Cancel any previous render task before starting a new one
      cancelRenderTask();
      void renderAllPages(scale);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scale, pdfLoaded]);

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 2)); // Max 2x
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 0.5)); // Min 0.5x

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!containerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - containerRef.current.offsetLeft);
    setStartY(e.pageY - containerRef.current.offsetTop);
    setScrollLeft(containerRef.current.scrollLeft);
    setScrollTop(containerRef.current.scrollTop);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    const x = e.pageX - containerRef.current.offsetLeft;
    const y = e.pageY - containerRef.current.offsetTop;
    containerRef.current.scrollLeft = scrollLeft - (x - startX);
    containerRef.current.scrollTop = scrollTop - (y - startY);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <div className={`flex flex-col overflow-y-auto ${className}`} style={styles}>
      <div className='relative h-[calc(100vh-100px)] flex-1 overflow-hidden rounded-lg border border-gray-200'>
        {pdfLoaded && !isLoading && (
          <div className='absolute top-4 right-4 mb-4 flex flex-col justify-end gap-2'>
            <Button
              variant='outline'
              size='lg'
              className={`size-10 ${scale >= 2 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-in'}`}
              onClick={zoomIn}
              disabled={scale >= 2}
              aria-label='Zoom in'
            >
              <Plus className='size-4' />
            </Button>
            <Button
              variant='outline'
              size='lg'
              className={`size-10 ${scale <= 0.5 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-out'}`}
              onClick={zoomOut}
              disabled={scale <= 0.5}
              aria-label='Zoom out'
            >
              <Minus className='size-4' />
            </Button>
          </div>
        )}
        {isPreviewDataLoading || !pdfLoaded ? (
          <div className='flex size-full items-center justify-center'>
            <div className='text-center'>
              <div className='border-t-primary-500 mx-auto mb-2 size-6 animate-spin rounded-full border-2 border-gray-300'></div>
              <p>Loading PDF...</p>
            </div>
          </div>
        ) : pdfLoaded ? (
          <div
            className='size-full cursor-grab overflow-auto select-none [&_*]:select-none [&_img]:pointer-events-none'
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            ref={containerRef}
          >
            <div className='flex flex-col items-center gap-8 p-8'>
              {renderedPages.map((canvas, index) => (
                <img
                  key={`${index + 1}`}
                  src={canvas.toDataURL()}
                  alt={`Page ${index + 1}`}
                  className='m-auto block max-w-none'
                />
              ))}
            </div>
          </div>
        ) : (
          <div className='flex h-full flex-col items-center justify-center p-4'>
            <p className='text-center text-sm text-gray-500'>
              {fileUrl && !isPreviewDataLoading ? (
                <Fragment>
                  Unable to display PDF.{' '}
                  <a
                    href={fileUrl}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='text-blue-500 hover:underline'
                  >
                    Click here
                  </a>{' '}
                  to open it in a new tab.
                </Fragment>
              ) : (
                'No PDF to preview'
              )}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PreviewPdf;
