'use client';

import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface CircularTimerProps {
  totalTime: number;
  currentTime: number;
  className?: string;
}

export function CircularTimer({ totalTime, currentTime, className }: CircularTimerProps) {
  const [isBlinking, setIsBlinking] = useState(false);

  const timeRemaining = Math.max(0, currentTime);
  const percentage = (timeRemaining / totalTime) * 100;

  const getColorState = () => {
    if (percentage <= 25) return 'critical';
    if (percentage <= 50) return 'warning';
    return 'normal';
  };

  const colorState = getColorState();

  useEffect(() => {
    if (colorState === 'critical') {
      const interval = setInterval(() => {
        setIsBlinking((prev) => !prev);
      }, 500);

      return () => clearInterval(interval);
    } else {
      setIsBlinking(false);
    }
  }, [colorState]);

  const radius = 20;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const formatTime = (seconds: number) => {
    return `${seconds}s`;
  };

  const getColors = () => {
    switch (colorState) {
      case 'critical':
        return {
          stroke: isBlinking ? '#ef4444' : '#dc2626',
          text: isBlinking ? 'text-red-500' : 'text-red-600',
          bg: 'stroke-red-200',
        };
      case 'warning':
        return {
          stroke: '#eab308',
          text: 'text-yellow-600',
          bg: 'stroke-yellow-200',
        };
      default:
        return {
          stroke: '#10b981',
          text: 'text-emerald-600',
          bg: 'stroke-emerald-200',
        };
    }
  };

  const colors = getColors();

  return (
    <div className={cn('text-gray-light flex items-center gap-2', className)}>
      <div className='relative'>
        <svg width='50' height='50' viewBox='0 0 50 50' className='-rotate-90 transform'>
          {/* Background circle */}
          <circle
            cx='25'
            cy='25'
            r={radius}
            stroke='currentColor'
            strokeWidth='3'
            fill='none'
            className={cn('opacity-20', colors.bg)}
          />

          {/* Progress circle */}
          <circle
            cx='25'
            cy='25'
            r={radius}
            stroke={colors.stroke}
            strokeWidth='3'
            fill='none'
            strokeLinecap='round'
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            className='transition-all duration-300 ease-in-out'
            style={{
              filter:
                colorState === 'critical' && isBlinking
                  ? 'drop-shadow(0 0 4px currentColor)'
                  : 'none',
            }}
          />
        </svg>

        <div className='absolute inset-0 flex items-center justify-center'>
          <div
            className={cn(
              'text-xs font-bold tabular-nums transition-colors duration-300',
              colors.text
            )}
          >
            {formatTime(timeRemaining)}
          </div>
        </div>
      </div>
    </div>
  );
}
