/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { LoaderIcon } from 'lucide-react';

interface DataTableProps<TData, TValue> extends React.ComponentPropsWithoutRef<'div'> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  isLoading?: boolean;
  onRowClick?: (row: TData) => void;
  limit?: number;
  wrapperClassName?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  title,
  isLoading,
  onRowClick,
  wrapperClassName,
  limit,
  ...props
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    defaultColumn: {
      enableSorting: false,
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: 0,
      },
    },
  });

  return (
    <section className={cn('mx-6', wrapperClassName)}>
      <div {...props}>
        <Table
          style={{
            borderSpacing: 0,
          }}
        >
          <TableHeader className='bg-custom-white w-full'>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className='hover:bg-custom-white border-none'>
                {headerGroup.headers.map((header, _index) => (
                  <TableHead
                    key={header.id}
                    style={{
                      width: header.getSize() !== 150 ? header.getSize() : undefined,
                      textAlign: (header.column.columnDef.meta as any)?.align,
                    }}
                    className='text-gray-light px-4'
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                className={`relative ${onRowClick ? 'cursor-pointer' : ''}`}
                onClick={onRowClick ? () => onRowClick(row.original) : undefined}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    style={{
                      textAlign: (cell.column.columnDef.meta as any)?.align,
                    }}
                    className='h-15 px-4 text-black'
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {!isLoading && table.getFilteredRowModel().rows.length === 0 && (
        <p className='py-20 text-center text-sm text-neutral-500 [word-spacing:1px]'>
          No Records Found
        </p>
      )}

      {isLoading && (
        <div className='flex items-center justify-center py-10'>
          <LoaderIcon className='text-primary size-8 animate-spin' />
        </div>
      )}
    </section>
  );
}
